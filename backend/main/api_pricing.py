from ninja_extra import api_controller, route
from .schemas import PricingPlanSchema
from typing import List


@api_controller("/pricing", tags=["定价"])
class PricingController:
    """定价相关API控制器"""

    @route.get("", response=List[PricingPlanSchema])
    def get_pricing_plans(self, request):
        """获取定价方案列表"""
        plans = [
            {
                "name": "free",
                "title": "免费版",
                "price": "¥0",
                "period": "永久",
                "features": [
                    "每月 5 篇文档/文献分析",
                    "单篇文献总结",
                    "单篇文献对话提问"
                ],
            },
            {
                "name": "premium",
                "title": "高级版",
                "price": "¥29",
                "period": "每月",
                "features": [
                    "每月 50 篇文档/文献分析",
                    "多篇文献总结",
                    "多篇文献对话提问",
                    "文献思维导图",
                ],
            },
            {
                "name": "plus",
                "title": "专业版",
                "price": "¥99",
                "period": "每月",
                "features": [
                    "无限文档/文献分析",
                    "多篇文献总结",
                    "多篇文献对话提问",
                    "文献思维导图",
                ],
            }
        ]
        return plans 