from ninja_extra import api_controller, route
from .schemas import PricingPlanSchema
from typing import List


@api_controller("/pricing", tags=["定价"])
class PricingController:
    """定价相关API控制器"""

    @route.get("", response=List[PricingPlanSchema])
    def get_pricing_plans(self, request):
        """获取定价方案列表"""
        plans = [
            {
                "name": "free",
                "title": "免费版",
                "price": "¥0",
                "period": "永久",
                "features": [
                    "每月 5 篇文档/文献分析",
                    "基础对话功能",
                    "基础文档处理",
                    "社区支持"
                ],
                "button_text": "当前方案",
                "button_class": "btn-outline-primary",
                "is_current": True
            },
            {
                "name": "premium",
                "title": "高级版",
                "price": "¥29",
                "period": "每月",
                "features": [
                    "每月 50 篇文档/文献分析",
                    "高级对话功能",
                    "完整文档处理",
                    "优先技术支持",
                    "自定义提示词"
                ],
                "button_text": "升级",
                "button_class": "btn-primary",
                "is_current": False
            },
            {
                "name": "plus",
                "title": "专业版",
                "price": "¥99",
                "period": "每月",
                "features": [
                    "无限文档/文献分析",
                    "所有高级版功能",
                    "API 访问",
                    "团队协作",
                    "专属客户经理"
                ],
            }
        ]
        return plans 