"""
URL configuration for appconfig project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
"""
from django.contrib import admin
from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from ninja import Swagger
from ninja_extra import NinjaExtraAPI
from main.api_auth import AuthController
from main.api_pricing import PricingController
from main.api_document import DocumentController
from llm_proxy.api import LLMController
from payment.antom.api import AntomPaymentController
from payment.payments.api import PaymentController

# 创建主API实例
api = NinjaExtraAPI(
    title="Z-Aiden API",
    version="1.0.0",
    description="Z-Aiden 平台API接口",
    docs=Swagger(
        settings={
            "persistAuthorization": True,
            "displayOperationId": True,
            "defaultModelsExpandDepth": -1  # 隐藏 Schema 部分
        }
    )
)

# 注册控制器
api.register_controllers(
    AuthController,
    PricingController,
    DocumentController,
    LLMController,
    AntomPaymentController,
    PaymentController,
)

urlpatterns = [
    path('admin/', admin.site.urls),  # Admin路由
    path('api/', api.urls),  # API路由
]

# 在开发环境中添加静态文件服务
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
